# GCSE History Paper 2: Enhanced Cold War Revision Website

A comprehensive, educational, and interactive revision website for Edexcel GCSE History Paper 2: Superpower Relations and the Cold War, 1941–91, featuring **teaching-before-testing** methodology.

## 🎯 Purpose

This website is designed specifically for GCSE students preparing for their History Paper 2 exam on **5 June 2025**. It covers all specification requirements with a clean, motivating interface perfect for students with low motivation who prefer clear, concise formats.

## ✨ Enhanced Features

### 📚 Learn Section (Teaching Before Testing)
- **Modular Content**: 9 bite-sized modules (3 per topic) for digestible learning
- **Progress Tracking**: Visual progress bar and completion status with localStorage persistence
- **Interactive Checkboxes**: Mark modules complete to unlock quizzes
- **Visual Icons**: Each module has relevant emojis for visual appeal
- **Accordion Interface**: Collapsible sections for organized content

### ⏰ Interactive Timeline
- **Dual Mode Interface**:
  - **Explore Events**: Browse and filter 26 key Cold War events
  - **Sort Challenge**: Drag-and-drop chronological ordering game
- **Educational Sorting**: 8 key events to memorize dates through interaction
- **Instant Feedback**: Correct/incorrect validation with explanations
- **Period Filtering**: Filter events by time periods (1941-1958, 1958-1970, 1970-1991)

### 🎯 Advanced Quiz System
- **Three Topic-Specific Quizzes**: Origins, Crises, and End of Cold War
- **Prerequisites System**: Must complete relevant modules to unlock quizzes
- **Multiple Question Types**:
  - Multiple Choice (4 options with explanations)
  - Fill-in-the-Gaps (case-insensitive validation)
  - Matching Exercises (drag-and-drop or clickable pairs)
- **Score Tracking**: Best scores saved and displayed
- **Randomization**: Shuffled questions and answers for replayability

### ❓ Practice Questions
- **Exam-Style Questions**: 8-mark source questions, 12-mark essays, 16-mark essays
- **Model Answers**: Detailed example responses with structure guidance
- **Auto-Save**: Answers saved to localStorage for review
- **Key Dates Reference**: Essential dates and facts for memorization

### 💡 Comprehensive Exam Tips
- **Question-Specific Strategies**: Different approaches for each question type
- **Time Management**: Detailed breakdown of exam timing
- **Essential Information**: Must-know dates, phrases, and quick facts
- **CPO Structure**: Content, Provenance, Own Knowledge for source questions

### 💾 Enhanced Offline Functionality
- **Complete Offline Access**: All features work without internet
- **Progress Persistence**: Module completion and quiz scores saved locally
- **Service Worker**: Caches all resources for offline use
- **No External Dependencies**: Only Tailwind CSS CDN required

## 🚀 Setup Instructions

### Option 1: Simple File Opening
1. Download both `index.html` and `sw.js` files
2. Save them in the same folder on your computer
3. Double-click `index.html` to open in your web browser
4. Bookmark the page for easy access

### Option 2: GitHub Pages (Recommended)
1. Create a new GitHub repository
2. Upload `index.html` and `sw.js` files
3. Go to Settings > Pages
4. Select "Deploy from a branch" and choose "main"
5. Your site will be available at `https://yourusername.github.io/repositoryname`

### Option 3: Local Server
```bash
# If you have Python installed
python -m http.server 8000

# If you have Node.js installed
npx serve .

# Then visit http://localhost:8000
```

## 📅 Enhanced Study Plan Integration

### Wednesday 28 May 2025 - Berlin Crisis Focus
**Morning Session (10:35 AM):**
1. **Learn Section**: Complete Module 2.1 (Berlin Crisis, 1958-61)
2. **Timeline Sort Challenge**: Practice with Berlin-related events (1958 Ultimatum, 1961 Wall)
3. **Timeline Explore**: Filter to 1958-1970 period, focus on Berlin events

**Evening Session (Post-4:00 PM):**
- **Practice Questions**: Attempt source question about Berlin Crisis
- **Quiz Preparation**: If Module 2.1-2.3 complete, try Crises Quiz

### Thursday 29 May 2025 - Source Skills Focus
**Afternoon Session (1:35 PM):**
1. **Practice Section**: Complete all source-based questions (8 marks)
2. **Exam Tips**: Review CPO structure (Content, Provenance, Own Knowledge)
3. **Learn Section**: Review any incomplete modules for context

**Evening Session:**
- **Timeline Sort**: Practice chronological ordering for date memorization
- **Quiz Review**: Retake any quizzes to improve scores

### Daily 15-20 Minute Sessions
**Rotation Schedule:**
- **Day 1**: Complete 1-2 Learn modules + mark complete
- **Day 2**: Timeline Sort Challenge + Explore mode
- **Day 3**: Practice Questions + review model answers
- **Day 4**: Quiz attempt (if modules unlocked) + review feedback
- **Day 5**: Exam Tips review + key dates memorization

**Progress Tracking:**
- Aim to complete all 9 modules by 2 June 2025
- Achieve 80%+ on all three quizzes
- Complete all practice questions with model answer review

## 🎯 How to Use Each Section

### Learn Section
- **Start Here**: Begin with Module 1.1 and work through systematically
- **Check Completion**: Mark modules complete using checkboxes
- **Track Progress**: Watch your progress bar fill as you complete modules
- **Unlock Quizzes**: Complete all modules in a topic to unlock its quiz
- **Visual Learning**: Each module has icons and clear bullet points

### Timeline Section
**Explore Events Tab:**
- Use filter buttons to focus on specific time periods
- Click any event card for detailed information
- Perfect for browsing and understanding chronology

**Sort Challenge Tab:**
- **Educational Game**: Drag 8 key events into chronological order
- **Instant Feedback**: Get immediate validation with explanations
- **Memory Aid**: Helps memorize essential dates through interaction
- **Reset & Retry**: Practice multiple times to master chronology

### Practice Questions
- **Auto-Save**: Type answers in text areas (automatically saved to localStorage)
- **Model Answers**: Click "Show Model Answer" for detailed example responses
- **Exam Preparation**: Covers all question types (8-mark, 12-mark, 16-mark)
- **Structure Guidance**: Learn proper essay and source analysis formats

### Quizzes Section
- **Prerequisites**: Complete required modules first (indicated by green/gray dots)
- **Three Quizzes**: Origins (1941-58), Crises (1958-70), End (1970-91)
- **Multiple Formats**: Multiple choice, fill-in-gaps, matching exercises
- **Score Tracking**: Best scores saved and displayed
- **Unlimited Attempts**: Retake quizzes to improve understanding

### Exam Tips
- **Question-Specific Strategies**: Different approaches for each question type
- **Time Management**: Detailed exam timing breakdown
- **Essential Information**: Must-know dates, phrases, and quick facts
- **Memory Aids**: Key dates organized by topic for easy memorization

## 🔧 Customization

### Adding More Questions
Edit the `quizQuestions` array in the JavaScript section:
```javascript
{
    question: "Your question here?",
    options: ["Option A", "Option B", "Option C", "Option D"],
    correct: 1, // Index of correct answer (0-3)
    explanation: "Explanation of the correct answer"
}
```

### Adding Timeline Events
Edit the `timelineEvents` array:
```javascript
{
    year: 1945,
    title: "Event Title",
    description: "Brief description",
    period: "1941-1958" // or "1958-1970" or "1970-1991"
}
```

### Adding Practice Questions
Add new sections following the existing pattern in the Practice Questions section.

## 🎓 Exam Success Tips

### For 5 June 2025 Exam
1. **Week Before**: Complete all practice questions, take quiz daily
2. **Day Before**: Review Timeline and Key Dates section
3. **Exam Day**: Remember the CPO structure for sources (Content, Provenance, Own Knowledge)

### Study Schedule Integration
- **Morning**: Review one Key Topic section (10 minutes)
- **Afternoon**: Practice one question type (15 minutes)
- **Evening**: Take quiz or review timeline (10 minutes)

## 🛠️ Technical Details

- **Framework**: Vanilla HTML, CSS, JavaScript (no external dependencies except Tailwind CSS)
- **Styling**: Tailwind CSS via CDN
- **Offline**: Service Worker caches all resources
- **Storage**: LocalStorage for user answers and progress
- **Compatibility**: Works in all modern browsers

## 📝 Future Enhancements

The site is designed to be easily expandable:
- Add Elizabethan England content for Paper 3
- Include more practice questions
- Add video explanations
- Create printable summary sheets

## 🆘 Troubleshooting

### Site Won't Load Offline
- Ensure both `index.html` and `sw.js` are in the same folder
- Try refreshing the page once while online to register the service worker

### Quiz Not Working
- Check that JavaScript is enabled in your browser
- Try refreshing the page

### Answers Not Saving
- Ensure your browser allows localStorage
- Check that you're not in private/incognito mode

## 📞 Support

This is a self-contained revision tool. All content follows the Edexcel GCSE History specification for Paper 2. For exam-specific questions, consult your teacher or the official specification document.

Good luck with your revision and exam on 5 June 2025! 🍀
