<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GCSE History Paper 2: Cold War Revision</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Enhanced styles for premium dark mode revision experience */
        :root {
            --primary-blue: #3b82f6;
            --primary-blue-dark: #1e40af;
            --success-green: #10b981;
            --error-red: #ef4444;
            --warning-yellow: #f59e0b;
            --purple-accent: #8b5cf6;
        }

        .correct {
            background: linear-gradient(135deg, #059669, #10b981) !important;
            border-color: var(--success-green) !important;
            color: #ecfdf5 !important;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
        }
        .incorrect {
            background: linear-gradient(135deg, #dc2626, #ef4444) !important;
            border-color: var(--error-red) !important;
            color: #fef2f2 !important;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
        }

        /* Enhanced Flashcards */
        .flashcard {
            transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
            transform-style: preserve-3d;
            cursor: pointer;
            height: 220px;
            border-radius: 16px;
            position: relative;
            background: linear-gradient(145deg, #374151, #4b5563);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .flashcard:hover {
            transform: scale(1.03) translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }
        .flashcard.flipped { transform: rotateY(180deg) scale(1.03); }
        .flashcard-front, .flashcard-back {
            backface-visibility: hidden;
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
            border-radius: 16px;
            border: 2px solid rgba(59, 130, 246, 0.3);
        }
        .flashcard-back {
            transform: rotateY(180deg);
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #1e40af 100%) !important;
            border: 2px solid var(--primary-blue) !important;
        }
        .flashcard-known {
            border-color: var(--success-green) !important;
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.3) !important;
        }

        /* Enhanced Timeline */
        .timeline-event {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, #374151, #4b5563);
            border: 2px solid #6b7280;
        }
        .timeline-event:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            border-color: var(--primary-blue);
        }
        .dragging {
            opacity: 0.8;
            transform: rotate(3deg) scale(1.05);
            box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
            border-color: var(--primary-blue) !important;
            z-index: 1000;
        }
        .drop-zone {
            border: 2px dashed var(--primary-blue);
            background: rgba(59, 130, 246, 0.1);
        }

        /* Enhanced Quiz Options */
        .quiz-option {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, #4b5563, #6b7280);
            border: 2px solid #6b7280;
        }
        .quiz-option:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            border-color: var(--primary-blue);
            background: linear-gradient(145deg, #6b7280, #9ca3af);
        }
        .quiz-option:active {
            transform: translateY(0);
        }

        /* Enhanced Progress Indicators */
        .progress-ring {
            transition: all 0.5s ease;
        }
        .progress-bar {
            background: linear-gradient(90deg, var(--primary-blue), var(--purple-accent));
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        /* Enhanced Animations */
        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .animate-slide-up { animation: slideInUp 0.6s ease-out; }
        .animate-pulse { animation: pulse 2s infinite; }
        .animate-shake { animation: shake 0.5s ease-in-out; }

        /* Enhanced Typography */
        .gradient-text {
            background: linear-gradient(135deg, var(--primary-blue), var(--purple-accent), #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradient-shift 3s ease infinite;
        }
        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Enhanced Sections */
        html { scroll-behavior: smooth; }
        .section {
            min-height: 100vh;
            padding-top: 90px;
            position: relative;
        }
        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--primary-blue), transparent);
        }

        /* Enhanced Cards */
        .enhanced-card {
            background: linear-gradient(145deg, #374151, #4b5563);
            border: 1px solid rgba(59, 130, 246, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .enhanced-card:hover {
            border-color: var(--primary-blue);
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
            transform: translateY(-2px);
        }

        /* Loading States */
        .loading {
            position: relative;
            overflow: hidden;
        }
        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: loading-shimmer 1.5s infinite;
        }
        @keyframes loading-shimmer {
            100% { left: 100%; }
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 font-sans">
    <!-- Enhanced Navigation -->
    <nav class="bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 shadow-2xl fixed top-0 w-full z-50 border-b border-gray-700">
        <div class="max-w-6xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="text-xl font-bold gradient-text flex items-center">
                    <span class="text-2xl mr-2">🌍</span>
                    <span>Cold War Revision</span>
                </div>
                <div class="hidden sm:flex space-x-1">
                    <a href="#home" class="nav-link px-4 py-2 rounded-lg text-gray-300 hover:text-blue-400 hover:bg-gray-700 transition-all duration-200 relative" aria-label="Home">
                        <span>Home</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#flashcards" class="nav-link px-4 py-2 rounded-lg text-gray-300 hover:text-blue-400 hover:bg-gray-700 transition-all duration-200 relative" aria-label="Flashcards">
                        <span>Flashcards</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#timeline" class="nav-link px-4 py-2 rounded-lg text-gray-300 hover:text-blue-400 hover:bg-gray-700 transition-all duration-200 relative" aria-label="Timeline">
                        <span>Timeline</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#quizzes" class="nav-link px-4 py-2 rounded-lg text-gray-300 hover:text-blue-400 hover:bg-gray-700 transition-all duration-200 relative" aria-label="Quizzes">
                        <span>Quizzes</span>
                        <div class="nav-indicator"></div>
                    </a>
                    <a href="#exam" class="nav-link px-4 py-2 rounded-lg text-gray-300 hover:text-blue-400 hover:bg-gray-700 transition-all duration-200 relative" aria-label="Exam Questions">
                        <span>Exam Questions</span>
                        <div class="nav-indicator"></div>
                    </a>
                </div>
                <button id="mobile-menu-btn" class="sm:hidden text-gray-300 hover:text-blue-400 p-2 rounded-lg hover:bg-gray-700 transition-all duration-200" aria-label="Toggle menu">
                    <svg class="w-6 h-6 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div id="mobile-menu" class="hidden sm:hidden pb-4 border-t border-gray-700 mt-4 pt-4">
                <a href="#home" class="block py-3 px-4 text-gray-300 hover:text-blue-400 hover:bg-gray-700 rounded-lg transition-all duration-200 mb-1">🏠 Home</a>
                <a href="#flashcards" class="block py-3 px-4 text-gray-300 hover:text-blue-400 hover:bg-gray-700 rounded-lg transition-all duration-200 mb-1">🃏 Flashcards</a>
                <a href="#timeline" class="block py-3 px-4 text-gray-300 hover:text-blue-400 hover:bg-gray-700 rounded-lg transition-all duration-200 mb-1">⏰ Timeline</a>
                <a href="#quizzes" class="block py-3 px-4 text-gray-300 hover:text-blue-400 hover:bg-gray-700 rounded-lg transition-all duration-200 mb-1">🎯 Quizzes</a>
                <a href="#exam" class="block py-3 px-4 text-gray-300 hover:text-blue-400 hover:bg-gray-700 rounded-lg transition-all duration-200">📝 Exam Questions</a>
            </div>
        </div>
    </nav>

    <!-- Home Section -->
    <section id="home" class="section bg-gradient-to-br from-gray-900 via-gray-900 to-gray-800">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <div class="mb-8">
                <h1 class="text-6xl font-bold mb-4 gradient-text">GCSE History Paper 2</h1>
                <h2 class="text-3xl text-blue-400 mb-6 font-semibold">Superpower Relations & the Cold War</h2>
                <div class="text-lg text-gray-300 mb-2">1941–91</div>
                <p class="text-xl mb-8 text-gray-200">Interactive revision for your exam on <strong class="text-blue-400 bg-blue-900 bg-opacity-30 px-3 py-1 rounded-lg">5 June 2025</strong></p>
            </div>

            <div class="bg-gray-800 rounded-lg p-8 mb-8 border border-gray-700">
                <h3 class="text-2xl font-semibold mb-6">📚 What's Included</h3>
                <div class="grid sm:grid-cols-2 gap-6 text-left">
                    <div class="group flex items-center p-5 bg-gradient-to-r from-gray-700 to-gray-600 rounded-xl border border-gray-600 hover:border-blue-500 transition-all duration-300 hover:transform hover:scale-105">
                        <div class="text-4xl mr-4 group-hover:scale-110 transition-transform duration-300">🃏</div>
                        <div>
                            <h4 class="font-bold text-blue-400 text-lg mb-1">Flashcards</h4>
                            <p class="text-sm text-gray-300 leading-relaxed">12 interactive cards with key facts</p>
                            <div class="text-xs text-blue-300 mt-1">Click to flip & learn</div>
                        </div>
                    </div>
                    <div class="group flex items-center p-5 bg-gradient-to-r from-gray-700 to-gray-600 rounded-xl border border-gray-600 hover:border-green-500 transition-all duration-300 hover:transform hover:scale-105">
                        <div class="text-4xl mr-4 group-hover:scale-110 transition-transform duration-300">⏰</div>
                        <div>
                            <h4 class="font-bold text-green-400 text-lg mb-1">Timeline Sorting</h4>
                            <p class="text-sm text-gray-300 leading-relaxed">Drag events into chronological order</p>
                            <div class="text-xs text-green-300 mt-1">Master key dates</div>
                        </div>
                    </div>
                    <div class="group flex items-center p-5 bg-gradient-to-r from-gray-700 to-gray-600 rounded-xl border border-gray-600 hover:border-purple-500 transition-all duration-300 hover:transform hover:scale-105">
                        <div class="text-4xl mr-4 group-hover:scale-110 transition-transform duration-300">🎯</div>
                        <div>
                            <h4 class="font-bold text-purple-400 text-lg mb-1">Quizzes</h4>
                            <p class="text-sm text-gray-300 leading-relaxed">3 topic quizzes with instant feedback</p>
                            <div class="text-xs text-purple-300 mt-1">Test your knowledge</div>
                        </div>
                    </div>
                    <div class="group flex items-center p-5 bg-gradient-to-r from-gray-700 to-gray-600 rounded-xl border border-gray-600 hover:border-yellow-500 transition-all duration-300 hover:transform hover:scale-105">
                        <div class="text-4xl mr-4 group-hover:scale-110 transition-transform duration-300">📝</div>
                        <div>
                            <h4 class="font-bold text-yellow-400 text-lg mb-1">Exam Practice</h4>
                            <p class="text-sm text-gray-300 leading-relaxed">Source questions and essay prompts</p>
                            <div class="text-xs text-yellow-300 mt-1">Real exam preparation</div>
                        </div>
                    </div>
                </div>
                <div class="mt-6 p-4 bg-blue-900 bg-opacity-50 rounded-lg border border-blue-700">
                    <p class="text-sm">
                        <strong>Perfect for low motivation!</strong> Quick 10-15 minute sessions with interactive content.
                        No boring notes - just engaging activities that help you learn and remember.
                    </p>
                </div>
            </div>

            <a href="#flashcards" class="bg-blue-600 text-white px-8 py-4 rounded-lg text-xl font-semibold hover:bg-blue-500 transition inline-block">
                🚀 Start Revising
            </a>
        </div>
    </section>

    <!-- Enhanced Flashcards Section -->
    <section id="flashcards" class="section bg-gradient-to-br from-gray-800 to-gray-900">
        <div class="max-w-6xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold gradient-text mb-4">🃏 Interactive Flashcards</h2>
                <p class="text-gray-300 text-lg">Master key facts with interactive flip cards</p>
            </div>

            <!-- Enhanced Controls -->
            <div class="enhanced-card rounded-xl p-6 mb-8">
                <div class="flex flex-col lg:flex-row justify-between items-center gap-6">
                    <div class="flex flex-col sm:flex-row items-center gap-4">
                        <label for="flashcard-filter" class="font-semibold text-gray-200">Filter by topic:</label>
                        <select id="flashcard-filter" class="bg-gray-600 border border-gray-500 rounded-lg px-4 py-3 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 transition-all">
                            <option value="all">📚 All Topics (12 cards)</option>
                            <option value="origins">🏛️ Origins 1941-58 (4 cards)</option>
                            <option value="crises">⚡ Crises 1958-70 (4 cards)</option>
                            <option value="end">🕊️ End 1970-91 (4 cards)</option>
                        </select>
                    </div>

                    <div class="flex items-center gap-6">
                        <!-- Progress Ring -->
                        <div class="relative">
                            <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                                <circle cx="32" cy="32" r="28" stroke="#374151" stroke-width="4" fill="none"/>
                                <circle id="flashcard-progress-ring" cx="32" cy="32" r="28" stroke="url(#gradient)" stroke-width="4" fill="none"
                                        stroke-dasharray="0 176" stroke-linecap="round" class="transition-all duration-500"/>
                                <defs>
                                    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                        <stop offset="0%" style="stop-color:#3b82f6"/>
                                        <stop offset="100%" style="stop-color:#8b5cf6"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span id="flashcard-progress-text" class="text-sm font-bold text-blue-400">0/12</span>
                            </div>
                        </div>

                        <div class="text-center">
                            <div id="flashcard-progress" class="text-lg font-semibold text-gray-200">0/12 known</div>
                            <div class="text-sm text-gray-400">Cards mastered</div>
                        </div>

                        <div class="flex gap-2">
                            <button id="study-mode-btn" class="bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium">
                                📖 Study Mode
                            </button>
                            <button id="reset-flashcards" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium">
                                🔄 Reset
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Study Mode Info -->
                <div id="study-mode-info" class="hidden mt-4 p-4 bg-blue-900 bg-opacity-30 rounded-lg border border-blue-700">
                    <div class="flex items-center gap-2 mb-2">
                        <span class="text-blue-400">📖</span>
                        <span class="font-semibold text-blue-300">Study Mode Active</span>
                    </div>
                    <p class="text-sm text-blue-200">Only showing cards you haven't mastered yet. Master all cards to complete this topic!</p>
                </div>
            </div>

            <!-- Flashcards Grid -->
            <div id="flashcards-container" class="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Flashcards will be loaded here by JavaScript -->
            </div>

            <!-- Study Statistics -->
            <div id="study-stats" class="mt-8 grid sm:grid-cols-3 gap-4">
                <div class="enhanced-card rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-green-400" id="mastered-count">0</div>
                    <div class="text-sm text-gray-400">Mastered</div>
                </div>
                <div class="enhanced-card rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-yellow-400" id="studying-count">12</div>
                    <div class="text-sm text-gray-400">Studying</div>
                </div>
                <div class="enhanced-card rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-blue-400" id="completion-percentage">0%</div>
                    <div class="text-sm text-gray-400">Complete</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Timeline Section -->
    <section id="timeline" class="section bg-gradient-to-br from-gray-900 to-gray-800">
        <div class="max-w-6xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold gradient-text mb-4">⏰ Timeline Sorting Challenge</h2>
                <p class="text-gray-300 text-lg">Master chronological order through interactive sorting</p>
            </div>

            <!-- Enhanced Controls -->
            <div class="enhanced-card rounded-xl p-6 mb-8">
                <div class="flex flex-col lg:flex-row justify-between items-center gap-6">
                    <div class="flex flex-col sm:flex-row items-center gap-4">
                        <label for="timeline-filter" class="font-semibold text-gray-200">Filter by topic:</label>
                        <select id="timeline-filter" class="bg-gray-600 border border-gray-500 rounded-lg px-4 py-3 text-gray-100 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 transition-all">
                            <option value="all">📅 All Events (8 events)</option>
                            <option value="origins">🏛️ Origins 1941-58 (3 events)</option>
                            <option value="crises">⚡ Crises 1958-70 (3 events)</option>
                            <option value="end">🕊️ End 1970-91 (2 events)</option>
                        </select>
                    </div>

                    <div class="flex items-center gap-4">
                        <!-- Challenge Stats -->
                        <div class="text-center">
                            <div class="text-lg font-semibold text-gray-200">
                                <span id="timeline-attempts">0</span> attempts
                            </div>
                            <div class="text-sm text-gray-400">This session</div>
                        </div>

                        <div class="text-center">
                            <div class="text-lg font-semibold text-green-400" id="timeline-best-score">0%</div>
                            <div class="text-sm text-gray-400">Best score</div>
                        </div>

                        <div class="flex gap-2">
                            <button id="shuffle-timeline" class="bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium">
                                🔀 Shuffle
                            </button>
                            <button id="check-timeline" class="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium">
                                ✅ Check Order
                            </button>
                            <button id="hint-timeline" class="bg-purple-600 hover:bg-purple-500 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium">
                                💡 Hint
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mt-4 p-4 bg-blue-900 bg-opacity-30 rounded-lg border border-blue-700">
                    <div class="flex items-center gap-2 mb-2">
                        <span class="text-blue-400">🎯</span>
                        <span class="font-semibold text-blue-300">How to Play</span>
                    </div>
                    <p class="text-sm text-blue-200">Drag and drop the events to arrange them in chronological order from earliest to latest. Click "Check Order" when you think you have it right!</p>
                </div>
            </div>

            <!-- Timeline Events Container -->
            <div class="relative">
                <div id="timeline-container" class="space-y-4 min-h-96">
                    <!-- Timeline events will be loaded here by JavaScript -->
                </div>

                <!-- Drop Zone Indicator -->
                <div id="drop-indicator" class="hidden absolute w-full h-1 bg-blue-500 rounded-full shadow-lg"></div>
            </div>

            <!-- Enhanced Feedback -->
            <div id="timeline-feedback" class="mt-8 hidden">
                <!-- Feedback will be shown here -->
            </div>

            <!-- Achievement System -->
            <div id="timeline-achievements" class="mt-8 grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="achievement-card enhanced-card rounded-lg p-4 text-center opacity-50" data-achievement="first-attempt">
                    <div class="text-2xl mb-2">🎯</div>
                    <div class="text-sm font-semibold text-gray-300">First Try</div>
                    <div class="text-xs text-gray-400">Complete your first sort</div>
                </div>
                <div class="achievement-card enhanced-card rounded-lg p-4 text-center opacity-50" data-achievement="perfect-score">
                    <div class="text-2xl mb-2">🏆</div>
                    <div class="text-sm font-semibold text-gray-300">Perfect Order</div>
                    <div class="text-xs text-gray-400">Get 100% correct</div>
                </div>
                <div class="achievement-card enhanced-card rounded-lg p-4 text-center opacity-50" data-achievement="speed-demon">
                    <div class="text-2xl mb-2">⚡</div>
                    <div class="text-sm font-semibold text-gray-300">Speed Demon</div>
                    <div class="text-xs text-gray-400">Perfect in under 30s</div>
                </div>
                <div class="achievement-card enhanced-card rounded-lg p-4 text-center opacity-50" data-achievement="persistent">
                    <div class="text-2xl mb-2">💪</div>
                    <div class="text-sm font-semibold text-gray-300">Persistent</div>
                    <div class="text-xs text-gray-400">Complete 10 attempts</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quizzes Section -->
    <section id="quizzes" class="section bg-gray-800">
        <div class="max-w-4xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-8">🎯 Interactive Quizzes</h2>

            <!-- Quiz Selection -->
            <div class="grid sm:grid-cols-3 gap-6 mb-8">
                <div class="quiz-card bg-gray-700 rounded-lg p-6 border border-gray-600 text-center cursor-pointer hover:bg-gray-600 transition" data-topic="origins">
                    <div class="text-4xl mb-4">🏛️</div>
                    <h3 class="text-xl font-bold text-blue-400 mb-2">Origins Quiz</h3>
                    <p class="text-gray-300 text-sm mb-4">1941-1958</p>
                    <div class="text-sm text-gray-400" id="score-origins">Best: Not attempted</div>
                </div>

                <div class="quiz-card bg-gray-700 rounded-lg p-6 border border-gray-600 text-center cursor-pointer hover:bg-gray-600 transition" data-topic="crises">
                    <div class="text-4xl mb-4">⚡</div>
                    <h3 class="text-xl font-bold text-green-400 mb-2">Crises Quiz</h3>
                    <p class="text-gray-300 text-sm mb-4">1958-1970</p>
                    <div class="text-sm text-gray-400" id="score-crises">Best: Not attempted</div>
                </div>

                <div class="quiz-card bg-gray-700 rounded-lg p-6 border border-gray-600 text-center cursor-pointer hover:bg-gray-600 transition" data-topic="end">
                    <div class="text-4xl mb-4">🕊️</div>
                    <h3 class="text-xl font-bold text-purple-400 mb-2">End Quiz</h3>
                    <p class="text-gray-300 text-sm mb-4">1970-1991</p>
                    <div class="text-sm text-gray-400" id="score-end">Best: Not attempted</div>
                </div>
            </div>

            <!-- Quiz Interface (Hidden by default) -->
            <div id="quiz-interface" class="hidden">
                <div class="bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl p-8 border border-gray-600 shadow-2xl">
                    <div class="flex justify-between items-center mb-8">
                        <h3 id="quiz-title" class="text-3xl font-bold gradient-text"></h3>
                        <button id="quit-quiz" class="text-gray-400 hover:text-red-400 text-2xl transition-colors duration-200 hover:scale-110 transform">✕</button>
                    </div>

                    <div class="mb-8">
                        <div class="flex justify-between items-center mb-3">
                            <span id="question-counter" class="text-lg font-semibold text-gray-200">Question 1/6</span>
                            <span id="score-display" class="text-lg font-semibold text-blue-400 bg-blue-900 bg-opacity-30 px-3 py-1 rounded-lg">Score: 0/0</span>
                        </div>
                        <div class="w-full bg-gray-600 rounded-full h-3 shadow-inner">
                            <div id="quiz-progress-bar" class="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500 shadow-sm" style="width: 16.67%"></div>
                        </div>
                        <div class="text-xs text-gray-400 mt-1 text-center">Progress through quiz</div>
                    </div>

                    <div id="question-container" class="mb-6">
                        <!-- Questions will be populated by JavaScript -->
                    </div>

                    <div class="flex justify-between">
                        <button id="prev-question" class="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-500 transition hidden">Previous</button>
                        <button id="next-question" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-500 transition hidden">Next Question</button>
                        <button id="finish-quiz" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-500 transition hidden">Finish Quiz</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Exam Questions Section -->
    <section id="exam" class="section bg-gray-900">
        <div class="max-w-4xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-8">📝 Exam Practice Questions</h2>

            <!-- Source Questions -->
            <div class="bg-gray-800 rounded-lg p-6 mb-8 border border-gray-700">
                <h3 class="text-xl font-bold mb-4 text-blue-400">📄 Source Questions (8 marks)</h3>

                <div class="mb-6">
                    <h4 class="font-semibold mb-2">Source A: Kennedy's Berlin Speech, 1963</h4>
                    <div class="bg-gray-700 p-4 rounded border-l-4 border-blue-500 italic text-gray-300">
                        "All free men, wherever they may live, are citizens of Berlin, and therefore, as a free man, I take pride in the words 'Ich bin ein Berliner' (I am a Berliner)."
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block font-semibold mb-2">How useful is Source A to a historian studying the impact of the Berlin Wall? (8 marks)</label>
                    <textarea id="source-answer-1" class="w-full h-32 p-3 bg-gray-700 border border-gray-600 rounded text-gray-100" placeholder="Remember: Content, Provenance, Own Knowledge (CPO)..."></textarea>
                </div>

                <button class="show-answer-btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-500 transition mb-4" onclick="toggleAnswer('model-answer-1')">
                    Show Model Answer
                </button>

                <div id="model-answer-1" class="hidden p-4 bg-green-900 bg-opacity-50 rounded border border-green-700">
                    <strong class="text-green-400">Model Answer Structure:</strong><br>
                    <strong class="text-green-300">Content:</strong> Shows Kennedy's support for West Berlin and defiance of Soviet control...<br>
                    <strong class="text-green-300">Provenance:</strong> Speech by US President in 1963, 2 years after Wall built. Reliable as official statement but biased towards Western view...<br>
                    <strong class="text-green-300">Own Knowledge:</strong> Berlin Wall built 1961 to stop refugee flow. Kennedy's visit boosted West Berlin morale...
                </div>
            </div>

            <!-- Essay Questions -->
            <div class="bg-gray-800 rounded-lg p-6 mb-8 border border-gray-700">
                <h3 class="text-xl font-bold mb-4 text-green-400">📝 Essay Questions</h3>

                <div class="mb-6">
                    <h4 class="font-semibold mb-2">Explain why the Cuban Missile Crisis occurred in 1962. (12 marks)</h4>
                    <textarea id="essay-answer-1" class="w-full h-40 p-3 bg-gray-700 border border-gray-600 rounded text-gray-100" placeholder="Structure: 2-3 reasons with evidence and explanation..."></textarea>
                </div>

                <button class="show-answer-btn bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-500 transition mb-4" onclick="toggleAnswer('model-answer-2')">
                    Show Essay Structure
                </button>

                <div id="model-answer-2" class="hidden p-4 bg-green-900 bg-opacity-50 rounded border border-green-700">
                    <strong class="text-green-400">Essay Structure:</strong><br>
                    <strong class="text-green-300">Reason 1:</strong> Bay of Pigs failure (1961) - Castro sought Soviet protection...<br>
                    <strong class="text-green-300">Reason 2:</strong> US missiles in Turkey - Khrushchev wanted to balance threat...<br>
                    <strong class="text-green-300">Reason 3:</strong> Cold War tensions - both sides testing resolve...
                </div>

                <div class="mt-6">
                    <h4 class="font-semibold mb-2">'The main reason for the end of the Cold War was Gorbachev's reforms.' How far do you agree? (16 marks)</h4>
                    <textarea id="essay-answer-2" class="w-full h-40 p-3 bg-gray-700 border border-gray-600 rounded text-gray-100" placeholder="Structure: Agree paragraph, Disagree paragraph, Conclusion with judgment..."></textarea>
                </div>
            </div>

            <!-- Exam Tips -->
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 class="text-xl font-bold mb-4 text-yellow-400">💡 Exam Tips for 5 June 2025</h3>
                <div class="grid sm:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold mb-2 text-blue-400">Source Questions (8 marks)</h4>
                        <ul class="text-sm space-y-1 text-gray-300">
                            <li>• <strong>Content:</strong> What does the source say?</li>
                            <li>• <strong>Provenance:</strong> Who, when, why? Reliable?</li>
                            <li>• <strong>Own Knowledge:</strong> Support or challenge</li>
                            <li>• <strong>Time:</strong> 10 minutes per question</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2 text-green-400">Essays (12/16 marks)</h4>
                        <ul class="text-sm space-y-1 text-gray-300">
                            <li>• <strong>12-mark:</strong> 2-3 reasons with evidence</li>
                            <li>• <strong>16-mark:</strong> Balanced argument + judgment</li>
                            <li>• <strong>Time:</strong> 15 min (12-mark), 20 min (16-mark)</li>
                            <li>• <strong>Structure:</strong> Intro, paragraphs, conclusion</li>
                        </ul>
                    </div>
                </div>
                <div class="mt-4 p-4 bg-yellow-900 bg-opacity-30 rounded border border-yellow-700">
                    <h4 class="font-semibold mb-2 text-yellow-400">Essential Dates</h4>
                    <div class="text-sm text-yellow-300 grid sm:grid-cols-3 gap-2">
                        <div>• 1945: Yalta</div>
                        <div>• 1947: Truman Doctrine</div>
                        <div>• 1961: Berlin Wall</div>
                        <div>• 1962: Cuban Crisis</div>
                        <div>• 1972: SALT 1</div>
                        <div>• 1989: Wall falls</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('./sw.js')
                .then(registration => console.log('SW registered'))
                .catch(error => console.log('SW registration failed'));
        }

        // Enhanced Global State Management
        let flashcardKnown = JSON.parse(localStorage.getItem('flashcardKnown') || '{}');
        let quizScores = JSON.parse(localStorage.getItem('quizScores') || '{}');
        let timelineStats = JSON.parse(localStorage.getItem('timelineStats') || '{"attempts": 0, "bestScore": 0, "achievements": []}');
        let studyMode = false;
        let currentFilter = 'all';
        let timelineStartTime = null;

        // Enhanced Mobile Menu with Animation
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            const icon = this.querySelector('svg');

            menu.classList.toggle('hidden');
            icon.classList.toggle('rotate-90');

            // Add slide animation
            if (!menu.classList.contains('hidden')) {
                menu.style.maxHeight = '0';
                menu.style.overflow = 'hidden';
                menu.style.transition = 'max-height 0.3s ease-out';
                setTimeout(() => {
                    menu.style.maxHeight = '300px';
                }, 10);
            }
        });

        // Enhanced Navigation Active State
        function updateActiveNavigation() {
            const sections = ['home', 'flashcards', 'timeline', 'quizzes', 'exam'];
            const navLinks = document.querySelectorAll('.nav-link');

            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const element = document.getElementById(section);
                    if (element) {
                        const rect = element.getBoundingClientRect();
                        if (rect.top <= 100 && rect.bottom >= 100) {
                            current = section;
                        }
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('text-blue-400', 'bg-gray-700');
                    if (link.getAttribute('href') === `#${current}`) {
                        link.classList.add('text-blue-400', 'bg-gray-700');
                    }
                });
            });
        }

        // Flashcard data (12 cards, 4 per topic)
        const flashcards = [
            // Origins (1941-58)
            { id: 1, topic: 'origins', front: 'When was the Yalta Conference?', back: '1945' },
            { id: 2, topic: 'origins', front: 'What was the Truman Doctrine?', back: 'US policy to contain communism (1947)' },
            { id: 3, topic: 'origins', front: 'How much was the Marshall Plan worth?', back: '$13 billion to rebuild Western Europe' },
            { id: 4, topic: 'origins', front: 'When was NATO formed?', back: '1949 - Western military alliance' },

            // Crises (1958-70)
            { id: 5, topic: 'crises', front: 'When was the Berlin Wall built?', back: '1961 - to stop East German refugees' },
            { id: 6, topic: 'crises', front: 'How long did the Cuban Missile Crisis last?', back: '13 days in October 1962' },
            { id: 7, topic: 'crises', front: 'What was the Prague Spring?', back: 'Dubček\'s reforms in Czechoslovakia (1968)' },
            { id: 8, topic: 'crises', front: 'What was the Brezhnev Doctrine?', back: 'USSR\'s right to intervene in socialist countries' },

            // End (1970-91)
            { id: 9, topic: 'end', front: 'When was SALT 1 signed?', back: '1972 - first arms limitation treaty' },
            { id: 10, topic: 'end', front: 'When did USSR invade Afghanistan?', back: '1979 - ended détente' },
            { id: 11, topic: 'end', front: 'What was Glasnost?', back: 'Gorbachev\'s policy of openness (1985)' },
            { id: 12, topic: 'end', front: 'When did the Berlin Wall fall?', back: '1989 - symbol of Cold War\'s end' }
        ];

        // Timeline events data (8 events for sorting)
        const timelineEvents = [
            { id: 1, year: 1945, title: 'Yalta Conference', topic: 'origins' },
            { id: 2, year: 1947, title: 'Truman Doctrine', topic: 'origins' },
            { id: 3, year: 1949, title: 'NATO formed', topic: 'origins' },
            { id: 4, year: 1961, title: 'Berlin Wall built', topic: 'crises' },
            { id: 5, year: 1962, title: 'Cuban Missile Crisis', topic: 'crises' },
            { id: 6, year: 1968, title: 'Prague Spring', topic: 'crises' },
            { id: 7, year: 1972, title: 'SALT 1 signed', topic: 'end' },
            { id: 8, year: 1989, title: 'Berlin Wall falls', topic: 'end' }
        ];

        // Quiz questions data (6 questions per topic)
        const quizQuestions = {
            origins: [
                { type: 'multiple', question: 'When was the Yalta Conference?', options: ['1944', '1945', '1946', '1947'], correct: 1 },
                { type: 'multiple', question: 'What was the main purpose of the Marshall Plan?', options: ['Military alliance', 'Rebuild Europe and prevent communism', 'Nuclear weapons', 'Divide Germany'], correct: 1 },
                { type: 'fill', question: 'The ___ Doctrine promised US aid to countries fighting communism.', answer: 'Truman' },
                { type: 'fill', question: 'NATO was formed in ___.', answer: '1949' },
                { type: 'match', question: 'Match the cause to consequence:', pairs: [['Marshall Plan', 'Economic recovery in Western Europe'], ['Berlin Blockade', 'Berlin Airlift']] },
                { type: 'match', question: 'Match the event to year:', pairs: [['Yalta Conference', '1945'], ['NATO formed', '1949']] }
            ],
            crises: [
                { type: 'multiple', question: 'When was the Berlin Wall built?', options: ['1960', '1961', '1962', '1963'], correct: 1 },
                { type: 'multiple', question: 'How long did the Cuban Missile Crisis last?', options: ['10 days', '13 days', '15 days', '20 days'], correct: 1 },
                { type: 'fill', question: 'The ___ Wall was built to stop East German refugees.', answer: 'Berlin' },
                { type: 'fill', question: 'The Prague Spring occurred in ___.', answer: '1968' },
                { type: 'match', question: 'Match the crisis to outcome:', pairs: [['Cuban Missile Crisis', 'Hotline established'], ['Prague Spring', 'Brezhnev Doctrine']] },
                { type: 'match', question: 'Match the leader to action:', pairs: [['Kennedy', 'Naval blockade of Cuba'], ['Dubček', 'Prague Spring reforms']] }
            ],
            end: [
                { type: 'multiple', question: 'When was SALT 1 signed?', options: ['1971', '1972', '1973', '1974'], correct: 1 },
                { type: 'multiple', question: 'What was Glasnost?', options: ['Restructuring', 'Openness', 'Arms control', 'Invasion'], correct: 1 },
                { type: 'fill', question: 'The USSR invaded Afghanistan in ___.', answer: '1979' },
                { type: 'fill', question: 'The Berlin Wall fell in ___.', answer: '1989' },
                { type: 'match', question: 'Match the policy to leader:', pairs: [['Glasnost', 'Gorbachev'], ['Détente', 'Nixon & Brezhnev']] },
                { type: 'match', question: 'Match the event to significance:', pairs: [['SALT 1', 'Arms limitation'], ['Helsinki Accords', 'Human rights']] }
            ]
        };

        // Enhanced Flashcard Loading with Study Mode
        function loadFlashcards(filter = 'all', forceStudyMode = false) {
            currentFilter = filter;
            const container = document.getElementById('flashcards-container');
            let filteredCards = filter === 'all' ? flashcards : flashcards.filter(card => card.topic === filter);

            // Apply study mode filter
            if (studyMode || forceStudyMode) {
                filteredCards = filteredCards.filter(card => !flashcardKnown[card.id]);
                document.getElementById('study-mode-info').classList.remove('hidden');
            } else {
                document.getElementById('study-mode-info').classList.add('hidden');
            }

            if (filteredCards.length === 0 && (studyMode || forceStudyMode)) {
                container.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <div class="text-6xl mb-4">🎉</div>
                        <h3 class="text-2xl font-bold text-green-400 mb-2">Congratulations!</h3>
                        <p class="text-gray-300 mb-4">You've mastered all cards in this topic!</p>
                        <button onclick="toggleStudyMode()" class="bg-blue-600 hover:bg-blue-500 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200">
                            📚 Review All Cards
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredCards.map(card => `
                <div class="flashcard ${flashcardKnown[card.id] ? 'flashcard-known' : ''} animate-slide-up"
                     data-id="${card.id}" onclick="flipCard(this)" style="animation-delay: ${Math.random() * 0.3}s">
                    <div class="flashcard-front">
                        <div class="text-center">
                            <div class="text-sm text-gray-400 mb-3 uppercase tracking-wide font-medium">${card.topic}</div>
                            <div class="text-xl font-bold text-gray-100 mb-4 leading-relaxed">${card.front}</div>
                            <div class="text-xs text-gray-400 flex items-center justify-center gap-2">
                                <span>Click to reveal</span>
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 12l-4-4h8l-4 4z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="flashcard-back">
                        <div class="text-center">
                            <div class="text-sm text-blue-200 mb-3 font-medium tracking-wide">ANSWER</div>
                            <div class="text-xl font-bold text-gray-100 mb-6 leading-relaxed">${card.back}</div>
                            <button class="mt-2 ${flashcardKnown[card.id] ? 'bg-green-700 hover:bg-green-600' : 'bg-gray-700 hover:bg-green-600'} text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg transform hover:scale-105" onclick="markKnown(event, ${card.id})">
                                ${flashcardKnown[card.id] ? '✅ Mastered' : '📚 Mark as Known'}
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            updateFlashcardProgress();
            updateStudyStats();
        }

        // Enhanced Flashcard Interactions
        function flipCard(cardElement) {
            if (cardElement.classList.contains('flipped')) {
                cardElement.classList.remove('flipped');
            } else {
                cardElement.classList.add('flipped');
                // Add a subtle shake animation when flipping
                setTimeout(() => {
                    cardElement.style.transform = 'rotateY(180deg) scale(1.03)';
                }, 100);
            }
        }

        function markKnown(event, cardId) {
            event.stopPropagation();
            const wasKnown = flashcardKnown[cardId];
            flashcardKnown[cardId] = !flashcardKnown[cardId];
            localStorage.setItem('flashcardKnown', JSON.stringify(flashcardKnown));

            const button = event.target;
            const card = button.closest('.flashcard');

            if (flashcardKnown[cardId]) {
                button.textContent = '✅ Mastered';
                button.className = 'mt-2 bg-green-700 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg transform hover:scale-105';
                card.classList.add('flashcard-known');

                // Celebration animation
                button.classList.add('animate-pulse');
                setTimeout(() => button.classList.remove('animate-pulse'), 1000);

                // Show achievement if first time
                if (!wasKnown) {
                    showFlashcardAchievement(cardId);
                }
            } else {
                button.textContent = '📚 Mark as Known';
                button.className = 'mt-2 bg-gray-700 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-lg transform hover:scale-105';
                card.classList.remove('flashcard-known');
            }

            updateFlashcardProgress();
            updateStudyStats();

            // Auto-reload in study mode if card was marked as known
            if (studyMode && flashcardKnown[cardId]) {
                setTimeout(() => {
                    loadFlashcards(currentFilter, true);
                }, 1500);
            }
        }

        function updateFlashcardProgress() {
            const totalCards = currentFilter === 'all' ? 12 : 4;
            const relevantCards = currentFilter === 'all' ? flashcards : flashcards.filter(card => card.topic === currentFilter);
            const known = relevantCards.filter(card => flashcardKnown[card.id]).length;

            // Update text progress
            document.getElementById('flashcard-progress').textContent = `${known}/${totalCards} known`;
            document.getElementById('flashcard-progress-text').textContent = `${known}/${totalCards}`;

            // Update progress ring
            const percentage = (known / totalCards) * 100;
            const circumference = 2 * Math.PI * 28; // radius = 28
            const offset = circumference - (percentage / 100) * circumference;
            document.getElementById('flashcard-progress-ring').style.strokeDasharray = `${circumference} ${circumference}`;
            document.getElementById('flashcard-progress-ring').style.strokeDashoffset = offset;
        }

        function updateStudyStats() {
            const totalCards = flashcards.length;
            const mastered = Object.values(flashcardKnown).filter(Boolean).length;
            const studying = totalCards - mastered;
            const percentage = Math.round((mastered / totalCards) * 100);

            document.getElementById('mastered-count').textContent = mastered;
            document.getElementById('studying-count').textContent = studying;
            document.getElementById('completion-percentage').textContent = `${percentage}%`;
        }

        function toggleStudyMode() {
            studyMode = !studyMode;
            const button = document.getElementById('study-mode-btn');

            if (studyMode) {
                button.textContent = '📚 All Cards';
                button.classList.remove('bg-blue-600', 'hover:bg-blue-500');
                button.classList.add('bg-purple-600', 'hover:bg-purple-500');
            } else {
                button.textContent = '📖 Study Mode';
                button.classList.remove('bg-purple-600', 'hover:bg-purple-500');
                button.classList.add('bg-blue-600', 'hover:bg-blue-500');
            }

            loadFlashcards(currentFilter, studyMode);
        }

        function showFlashcardAchievement(cardId) {
            const achievements = {
                1: "First Yalta fact mastered! 🏛️",
                5: "Berlin Wall knowledge locked in! 🧱",
                9: "SALT 1 expertise achieved! 🕊️"
            };

            if (achievements[cardId]) {
                const notification = document.createElement('div');
                notification.className = 'fixed top-20 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-slide-up';
                notification.innerHTML = `
                    <div class="flex items-center gap-2">
                        <span class="text-xl">🎉</span>
                        <span class="font-medium">${achievements[cardId]}</span>
                    </div>
                `;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }
        }

        // Timeline functionality
        let currentTimelineEvents = [];

        function loadTimeline(filter = 'all') {
            currentTimelineEvents = filter === 'all' ? [...timelineEvents] : timelineEvents.filter(event => event.topic === filter);
            shuffleTimeline();
        }

        function shuffleTimeline() {
            currentTimelineEvents = currentTimelineEvents.sort(() => Math.random() - 0.5);
            renderTimeline();
        }

        function renderTimeline() {
            const container = document.getElementById('timeline-container');
            container.innerHTML = currentTimelineEvents.map((event, index) => `
                <div class="timeline-event bg-gray-800 border border-gray-600 p-5 rounded-lg cursor-move transition-all duration-200 hover:border-blue-500"
                     draggable="true" data-year="${event.year}" data-index="${index}">
                    <div class="flex items-center">
                        <div class="bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-xl w-20 h-20 flex items-center justify-center text-sm font-bold mr-5 shadow-lg">
                            <div class="text-center">
                                <div class="text-lg font-bold">${event.year}</div>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold text-lg text-gray-100 mb-1">${event.title}</h4>
                            <div class="flex items-center">
                                <span class="text-xs text-gray-400 uppercase tracking-wide font-medium px-2 py-1 bg-gray-700 rounded">${event.topic}</span>
                                <span class="ml-2 text-gray-500">📅</span>
                            </div>
                        </div>
                        <div class="text-gray-500 ml-4">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                            </svg>
                        </div>
                    </div>
                </div>
            `).join('');

            // Add drag and drop listeners
            document.querySelectorAll('.timeline-event').forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
                item.addEventListener('dragover', handleDragOver);
                item.addEventListener('drop', handleDrop);
                item.addEventListener('dragend', handleDragEnd);
            });
        }

        let draggedElement = null;

        function handleDragStart(e) {
            draggedElement = this;
            this.classList.add('dragging');
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDrop(e) {
            e.preventDefault();
            if (this !== draggedElement) {
                const container = document.getElementById('timeline-container');
                const allItems = [...container.children];
                const draggedIndex = allItems.indexOf(draggedElement);
                const targetIndex = allItems.indexOf(this);

                if (draggedIndex < targetIndex) {
                    container.insertBefore(draggedElement, this.nextSibling);
                } else {
                    container.insertBefore(draggedElement, this);
                }
            }
        }

        function handleDragEnd(e) {
            this.classList.remove('dragging');
            draggedElement = null;
        }

        function checkTimelineOrder() {
            const container = document.getElementById('timeline-container');
            const currentOrder = [...container.children].map(item => parseInt(item.dataset.year));
            const correctOrder = currentTimelineEvents.map(event => event.year).sort((a, b) => a - b);

            const isCorrect = JSON.stringify(currentOrder) === JSON.stringify(correctOrder);
            const feedback = document.getElementById('timeline-feedback');

            if (isCorrect) {
                feedback.innerHTML = `
                    <div class="bg-green-800 border border-green-600 rounded-lg p-4">
                        <h4 class="font-bold text-green-400 mb-2">🎉 Perfect! Excellent chronological order!</h4>
                        <p class="text-green-300">You've correctly arranged all events from ${correctOrder[0]} to ${correctOrder[correctOrder.length - 1]}.</p>
                    </div>
                `;
            } else {
                const score = currentOrder.filter((year, index) => year === correctOrder[index]).length;
                feedback.innerHTML = `
                    <div class="bg-red-800 border border-red-600 rounded-lg p-4">
                        <h4 class="font-bold text-red-400 mb-2">❌ Not quite right. Keep trying!</h4>
                        <p class="text-red-300">You got ${score} out of ${correctOrder.length} positions correct.</p>
                        <p class="text-red-300 mt-2">Hint: Remember the chronological order of major Cold War events!</p>
                    </div>
                `;
            }

            feedback.classList.remove('hidden');
        }

        // Event listeners
        document.getElementById('flashcard-filter').addEventListener('change', function() {
            loadFlashcards(this.value);
        });

        document.getElementById('reset-flashcards').addEventListener('click', function() {
            flashcardKnown = {};
            localStorage.setItem('flashcardKnown', JSON.stringify(flashcardKnown));
            loadFlashcards(document.getElementById('flashcard-filter').value);
        });

        document.getElementById('timeline-filter').addEventListener('change', function() {
            loadTimeline(this.value);
        });

        document.getElementById('shuffle-timeline').addEventListener('click', shuffleTimeline);
        document.getElementById('check-timeline').addEventListener('click', checkTimelineOrder);

        // Enhanced Quiz functionality with proper scoring
        let currentQuiz = null;
        let currentQuestionIndex = 0;
        let quizAnswers = [];
        let quizScore = 0;
        let questionAnswered = false;

        function startQuiz(topic) {
            currentQuiz = topic;
            currentQuestionIndex = 0;
            quizAnswers = [];
            quizScore = 0;
            questionAnswered = false;

            document.querySelector('.grid.sm\\:grid-cols-3').classList.add('hidden');
            document.getElementById('quiz-interface').classList.remove('hidden');
            document.getElementById('quiz-title').textContent = `${topic.charAt(0).toUpperCase() + topic.slice(1)} Quiz`;

            loadQuestion();
        }

        function loadQuestion() {
            const questions = quizQuestions[currentQuiz];
            const question = questions[currentQuestionIndex];
            const container = document.getElementById('question-container');
            questionAnswered = false;

            // Update progress indicators
            document.getElementById('question-counter').textContent = `Question ${currentQuestionIndex + 1}/6`;
            document.getElementById('score-display').textContent = `Score: ${quizScore}/${Math.max(currentQuestionIndex, 1)}`;
            document.getElementById('quiz-progress-bar').style.width = `${((currentQuestionIndex + 1) / 6) * 100}%`;

            if (question.type === 'multiple') {
                container.innerHTML = `
                    <div class="question">
                        <h4 class="text-lg font-semibold mb-4">${question.question}</h4>
                        <div class="space-y-2">
                            ${question.options.map((option, index) => `
                                <button class="quiz-option w-full text-left p-3 bg-gray-600 rounded hover:bg-gray-500 transition border border-gray-500"
                                        data-index="${index}" onclick="selectAnswer(${index})">
                                    ${String.fromCharCode(65 + index)}) ${option}
                                </button>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else if (question.type === 'fill') {
                container.innerHTML = `
                    <div class="question">
                        <h4 class="text-lg font-semibold mb-4">${question.question}</h4>
                        <input type="text" id="fill-answer" class="w-full p-3 bg-gray-600 border border-gray-500 rounded text-gray-100"
                               placeholder="Type your answer here..." onkeyup="checkFillAnswer()">
                    </div>
                `;
            } else if (question.type === 'match') {
                // Shuffle the right side options for matching
                const shuffledAnswers = [...question.pairs.map((pair, index) => ({text: pair[1], correctIndex: index}))];
                shuffledAnswers.sort(() => Math.random() - 0.5);

                container.innerHTML = `
                    <div class="question">
                        <h4 class="text-lg font-semibold mb-4">${question.question}</h4>
                        <p class="text-sm text-gray-400 mb-4">Click on items from the right column to match them with items on the left.</p>
                        <div class="grid sm:grid-cols-2 gap-6">
                            <div class="space-y-3">
                                <h5 class="text-sm font-semibold text-blue-400 mb-2">Match these:</h5>
                                ${question.pairs.map((pair, index) => `
                                    <div class="match-left p-4 bg-gray-600 rounded-lg border border-gray-500 transition-all" data-index="${index}">
                                        <div class="font-medium">${pair[0]}</div>
                                        <div class="match-result text-sm mt-2 hidden"></div>
                                    </div>
                                `).join('')}
                            </div>
                            <div class="space-y-3">
                                <h5 class="text-sm font-semibold text-blue-400 mb-2">With these:</h5>
                                ${shuffledAnswers.map((item, index) => `
                                    <button class="match-option w-full text-left p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-all border border-gray-600 font-medium"
                                            data-correct-index="${item.correctIndex}" onclick="selectMatch(this, ${item.correctIndex})">
                                        ${item.text}
                                    </button>
                                `).join('')}
                            </div>
                        </div>
                        <div class="mt-6">
                            <button id="check-matches" class="bg-green-600 hover:bg-green-500 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium hidden" onclick="checkMatches()">
                                ✅ Check Matches
                            </button>
                        </div>
                    </div>
                `;
            }

            updateQuizButtons();
        }

        function selectAnswer(index) {
            if (questionAnswered) return; // Prevent multiple answers

            const question = quizQuestions[currentQuiz][currentQuestionIndex];
            const options = document.querySelectorAll('.quiz-option');
            const isCorrect = index === question.correct;

            questionAnswered = true;

            options.forEach((opt, i) => {
                opt.disabled = true;
                opt.style.pointerEvents = 'none';

                if (i === question.correct) {
                    // Always show correct answer in green
                    opt.classList.add('correct');
                    opt.classList.remove('bg-gray-600', 'hover:bg-gray-500');
                } else if (i === index && i !== question.correct) {
                    // Only show red for the wrong answer the user selected
                    opt.classList.add('incorrect');
                    opt.classList.remove('bg-gray-600', 'hover:bg-gray-500');
                } else {
                    // Leave other options unchanged but disabled
                    opt.classList.add('opacity-60');
                }
            });

            // Update score if correct
            if (isCorrect) {
                quizScore++;
            }

            // Update score display immediately
            document.getElementById('score-display').textContent = `Score: ${quizScore}/${currentQuestionIndex + 1}`;

            // Store answer
            quizAnswers[currentQuestionIndex] = index;

            // Show feedback message
            const container = document.getElementById('question-container');
            let feedback = container.querySelector('.quiz-feedback');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = 'quiz-feedback mt-4 p-3 rounded-lg';
                container.appendChild(feedback);
            }

            if (isCorrect) {
                feedback.className = 'quiz-feedback mt-4 p-3 rounded-lg bg-green-900 bg-opacity-50 border border-green-600';
                feedback.innerHTML = '<span class="text-green-400 font-semibold">✅ Correct!</span> <span class="text-green-300">Well done!</span>';
            } else {
                feedback.className = 'quiz-feedback mt-4 p-3 rounded-lg bg-red-900 bg-opacity-50 border border-red-600';
                feedback.innerHTML = '<span class="text-red-400 font-semibold">❌ Incorrect.</span> <span class="text-red-300">The correct answer is highlighted in green.</span>';
            }

            // Show next button after brief delay
            setTimeout(() => {
                document.getElementById('next-question').classList.remove('hidden');
            }, 800);
        }

        function checkFillAnswer() {
            const input = document.getElementById('fill-answer');
            const question = quizQuestions[currentQuiz][currentQuestionIndex];
            const userAnswer = input.value.toLowerCase().trim();
            const correctAnswer = question.answer.toLowerCase();

            if (userAnswer === correctAnswer) {
                input.classList.add('correct');
                input.classList.remove('bg-gray-600', 'border-gray-500');
                input.disabled = true;
                quizScore++;
                quizAnswers[currentQuestionIndex] = input.value;

                // Show success feedback
                const feedback = document.createElement('div');
                feedback.className = 'mt-2 text-green-400 text-sm font-medium';
                feedback.textContent = '✅ Correct!';
                input.parentNode.appendChild(feedback);

                setTimeout(() => {
                    document.getElementById('next-question').classList.remove('hidden');
                }, 600);
            } else if (userAnswer.length >= correctAnswer.length) {
                // Only show incorrect if they've typed enough characters
                input.classList.add('incorrect');
                input.classList.remove('bg-gray-600', 'border-gray-500');
                input.disabled = true;
                quizAnswers[currentQuestionIndex] = input.value;

                // Show correct answer
                const feedback = document.createElement('div');
                feedback.className = 'mt-2 text-red-400 text-sm font-medium';
                feedback.innerHTML = `❌ Incorrect. The answer was: <span class="text-green-400">${question.answer}</span>`;
                input.parentNode.appendChild(feedback);

                setTimeout(() => {
                    document.getElementById('next-question').classList.remove('hidden');
                }, 1000);
            }
        }

        // Enhanced matching functionality
        let selectedMatches = {};
        let matchingComplete = false;

        function selectMatch(button, correctIndex) {
            if (matchingComplete) return;

            const question = quizQuestions[currentQuiz][currentQuestionIndex];
            const leftItems = document.querySelectorAll('.match-left');

            // Find which left item this should match with
            const targetLeftItem = leftItems[correctIndex];

            // Check if this left item already has a match
            if (selectedMatches[correctIndex]) {
                // Remove previous match
                const prevButton = selectedMatches[correctIndex];
                prevButton.classList.remove('bg-blue-600', 'border-blue-500');
                prevButton.classList.add('bg-gray-700', 'border-gray-600');
                prevButton.disabled = false;

                const prevResult = targetLeftItem.querySelector('.match-result');
                prevResult.classList.add('hidden');
            }

            // Set new match
            selectedMatches[correctIndex] = button;
            button.classList.remove('bg-gray-700', 'border-gray-600');
            button.classList.add('bg-blue-600', 'border-blue-500');
            button.disabled = true;

            // Show what was matched
            const result = targetLeftItem.querySelector('.match-result');
            result.textContent = `→ ${button.textContent}`;
            result.classList.remove('hidden');
            result.classList.add('text-blue-300');

            // Check if all matches are made
            if (Object.keys(selectedMatches).length === question.pairs.length) {
                document.getElementById('check-matches').classList.remove('hidden');
            }
        }

        function checkMatches() {
            if (questionAnswered) return;

            const question = quizQuestions[currentQuiz][currentQuestionIndex];
            const leftItems = document.querySelectorAll('.match-left');
            let correctMatches = 0;

            questionAnswered = true;
            matchingComplete = true;

            // Check each match
            leftItems.forEach((leftItem, index) => {
                const result = leftItem.querySelector('.match-result');
                const selectedButton = selectedMatches[index];

                if (selectedButton && selectedButton.dataset.correctIndex == index) {
                    // Correct match
                    correctMatches++;
                    leftItem.classList.add('border-green-500', 'bg-green-900', 'bg-opacity-30');
                    result.classList.remove('text-blue-300');
                    result.classList.add('text-green-400');
                    result.textContent = `✅ ${selectedButton.textContent}`;
                    selectedButton.classList.remove('bg-blue-600');
                    selectedButton.classList.add('bg-green-600');
                } else {
                    // Incorrect or missing match
                    leftItem.classList.add('border-red-500', 'bg-red-900', 'bg-opacity-30');
                    if (selectedButton) {
                        result.classList.remove('text-blue-300');
                        result.classList.add('text-red-400');
                        result.textContent = `❌ ${selectedButton.textContent}`;
                        selectedButton.classList.remove('bg-blue-600');
                        selectedButton.classList.add('bg-red-600');
                    }

                    // Show correct answer
                    const correctAnswer = question.pairs[index][1];
                    const correctDiv = document.createElement('div');
                    correctDiv.className = 'text-green-400 text-sm mt-1';
                    correctDiv.textContent = `Correct: ${correctAnswer}`;
                    result.parentNode.appendChild(correctDiv);
                }
            });

            // Calculate score (give points based on percentage correct)
            const percentage = (correctMatches / question.pairs.length);
            if (percentage >= 0.5) { // Give credit if at least half correct
                quizScore++;
            }

            // Update score display
            document.getElementById('score-display').textContent = `Score: ${quizScore}/${currentQuestionIndex + 1}`;

            // Store answer
            quizAnswers[currentQuestionIndex] = selectedMatches;

            // Show feedback
            const container = document.getElementById('question-container');
            let feedback = container.querySelector('.quiz-feedback');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = 'quiz-feedback mt-4 p-3 rounded-lg';
                container.appendChild(feedback);
            }

            if (correctMatches === question.pairs.length) {
                feedback.className = 'quiz-feedback mt-4 p-3 rounded-lg bg-green-900 bg-opacity-50 border border-green-600';
                feedback.innerHTML = `<span class="text-green-400 font-semibold">✅ Perfect!</span> <span class="text-green-300">All ${correctMatches} matches correct!</span>`;
            } else if (percentage >= 0.5) {
                feedback.className = 'quiz-feedback mt-4 p-3 rounded-lg bg-yellow-900 bg-opacity-50 border border-yellow-600';
                feedback.innerHTML = `<span class="text-yellow-400 font-semibold">⚡ Good effort!</span> <span class="text-yellow-300">${correctMatches}/${question.pairs.length} matches correct.</span>`;
            } else {
                feedback.className = 'quiz-feedback mt-4 p-3 rounded-lg bg-red-900 bg-opacity-50 border border-red-600';
                feedback.innerHTML = `<span class="text-red-400 font-semibold">❌ Keep trying!</span> <span class="text-red-300">${correctMatches}/${question.pairs.length} matches correct.</span>`;
            }

            // Hide check button and show next
            document.getElementById('check-matches').classList.add('hidden');
            setTimeout(() => {
                document.getElementById('next-question').classList.remove('hidden');
            }, 1000);
        }

        function nextQuestion() {
            // Reset matching state for next question
            selectedMatches = {};
            matchingComplete = false;

            currentQuestionIndex++;
            if (currentQuestionIndex < 6) {
                loadQuestion();
            } else {
                finishQuiz();
            }
        }

        function finishQuiz() {
            const percentage = Math.round((quizScore / 6) * 100);
            quizScores[currentQuiz] = Math.max(quizScores[currentQuiz] || 0, quizScore);
            localStorage.setItem('quizScores', JSON.stringify(quizScores));

            document.getElementById('question-container').innerHTML = `
                <div class="text-center">
                    <h3 class="text-2xl font-bold mb-4">Quiz Complete!</h3>
                    <div class="text-4xl mb-4">${percentage >= 70 ? '🎉' : percentage >= 50 ? '👍' : '📚'}</div>
                    <p class="text-xl mb-2">Score: ${quizScore}/6 (${percentage}%)</p>
                    <p class="text-gray-300 mb-6">${percentage >= 70 ? 'Excellent work!' : percentage >= 50 ? 'Good effort!' : 'Keep studying!'}</p>
                    <button onclick="exitQuiz()" class="bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-500 transition">
                        Back to Quizzes
                    </button>
                </div>
            `;

            document.getElementById('next-question').classList.add('hidden');
            document.getElementById('finish-quiz').classList.add('hidden');

            updateQuizScores();
        }

        function exitQuiz() {
            document.querySelector('.grid.sm\\:grid-cols-3').classList.remove('hidden');
            document.getElementById('quiz-interface').classList.add('hidden');
            currentQuiz = null;
        }

        function updateQuizButtons() {
            document.getElementById('prev-question').classList.toggle('hidden', currentQuestionIndex === 0);
            document.getElementById('next-question').classList.add('hidden');
            document.getElementById('finish-quiz').classList.toggle('hidden', currentQuestionIndex < 5);
        }

        function updateQuizScores() {
            Object.keys(quizScores).forEach(topic => {
                const scoreElement = document.getElementById(`score-${topic}`);
                if (scoreElement) {
                    scoreElement.textContent = `Best: ${quizScores[topic]}/6`;
                }
            });
        }

        // Exam answer saving
        function saveAnswer(textareaId) {
            const textarea = document.getElementById(textareaId);
            localStorage.setItem(textareaId, textarea.value);
        }

        function loadSavedAnswers() {
            ['source-answer-1', 'essay-answer-1', 'essay-answer-2'].forEach(id => {
                const textarea = document.getElementById(id);
                const saved = localStorage.getItem(id);
                if (saved && textarea) {
                    textarea.value = saved;
                }
            });
        }

        function toggleAnswer(answerId) {
            const answer = document.getElementById(answerId);
            answer.classList.toggle('hidden');
        }

        // Quiz card event listeners
        document.querySelectorAll('.quiz-card').forEach(card => {
            card.addEventListener('click', function() {
                startQuiz(this.dataset.topic);
            });
        });

        // Quiz navigation
        document.getElementById('quit-quiz').addEventListener('click', exitQuiz);
        document.getElementById('next-question').addEventListener('click', nextQuestion);
        document.getElementById('finish-quiz').addEventListener('click', finishQuiz);

        // Enhanced Event Listeners
        document.getElementById('flashcard-filter').addEventListener('change', function() {
            loadFlashcards(this.value);
        });

        document.getElementById('study-mode-btn').addEventListener('click', toggleStudyMode);

        document.getElementById('reset-flashcards').addEventListener('click', function() {
            if (confirm('Are you sure you want to reset all flashcard progress? This cannot be undone.')) {
                flashcardKnown = {};
                localStorage.setItem('flashcardKnown', JSON.stringify(flashcardKnown));
                loadFlashcards(document.getElementById('flashcard-filter').value);

                // Show reset notification
                const notification = document.createElement('div');
                notification.className = 'fixed top-20 right-4 bg-blue-600 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-slide-up';
                notification.innerHTML = `
                    <div class="flex items-center gap-2">
                        <span class="text-xl">🔄</span>
                        <span class="font-medium">Flashcard progress reset!</span>
                    </div>
                `;
                document.body.appendChild(notification);
                setTimeout(() => notification.remove(), 3000);
            }
        });

        document.getElementById('timeline-filter').addEventListener('change', function() {
            loadTimeline(this.value);
        });

        document.getElementById('shuffle-timeline').addEventListener('click', shuffleTimeline);
        document.getElementById('check-timeline').addEventListener('click', checkTimelineOrder);

        // Auto-save exam answers
        ['source-answer-1', 'essay-answer-1', 'essay-answer-2'].forEach(id => {
            const textarea = document.getElementById(id);
            if (textarea) {
                textarea.addEventListener('input', () => saveAnswer(id));
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced Initialization
        function initializeApp() {
            // Load all sections
            loadFlashcards();
            loadTimeline();
            updateQuizScores();
            loadSavedAnswers();
            updateActiveNavigation();

            // Update all progress indicators
            updateFlashcardProgress();
            updateStudyStats();

            // Load timeline stats
            document.getElementById('timeline-attempts').textContent = timelineStats.attempts;
            document.getElementById('timeline-best-score').textContent = `${timelineStats.bestScore}%`;

            console.log('🌍 Cold War Revision App Initialized Successfully!');
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }
    </script>
</body>
</html>
