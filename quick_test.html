<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Test - GCSE History Website</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .pending { background-color: #fff3cd; color: #856404; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🧪 Quick Test Suite for GCSE History Website</h1>
    <p>This will test your main website functionality. Make sure your main site is running first!</p>
    
    <button onclick="runAllTests()">🚀 Run All Tests</button>
    <button onclick="clearResults()">🗑️ Clear Results</button>
    
    <div id="test-results"></div>

    <script>
        const results = document.getElementById('test-results');
        
        function addResult(testName, status, message) {
            const div = document.createElement('div');
            div.className = `test-result ${status}`;
            div.innerHTML = `<strong>${testName}:</strong> ${message}`;
            results.appendChild(div);
        }
        
        function clearResults() {
            results.innerHTML = '';
        }
        
        async function runAllTests() {
            clearResults();
            addResult('Test Suite', 'pending', 'Starting tests...');
            
            // Test 1: Check if main files exist
            try {
                const response = await fetch('./index.html');
                if (response.ok) {
                    addResult('File Check', 'pass', 'index.html found and accessible');
                } else {
                    addResult('File Check', 'fail', 'index.html not accessible');
                }
            } catch (error) {
                addResult('File Check', 'fail', 'Cannot access index.html: ' + error.message);
            }
            
            // Test 2: Check Service Worker file
            try {
                const response = await fetch('./sw.js');
                if (response.ok) {
                    addResult('Service Worker', 'pass', 'sw.js found and accessible');
                } else {
                    addResult('Service Worker', 'fail', 'sw.js not accessible');
                }
            } catch (error) {
                addResult('Service Worker', 'fail', 'Cannot access sw.js: ' + error.message);
            }
            
            // Test 3: Check localStorage functionality
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                if (value === 'value') {
                    addResult('Local Storage', 'pass', 'localStorage working correctly');
                } else {
                    addResult('Local Storage', 'fail', 'localStorage not working');
                }
            } catch (error) {
                addResult('Local Storage', 'fail', 'localStorage error: ' + error.message);
            }
            
            // Test 4: Check if running on localhost or file protocol
            const protocol = window.location.protocol;
            if (protocol === 'http:' || protocol === 'https:') {
                addResult('Protocol', 'pass', `Running on ${protocol} - Service Worker will work`);
            } else {
                addResult('Protocol', 'pending', `Running on ${protocol} - Service Worker may not work (use localhost for full testing)`);
            }
            
            // Test 5: Check screen size responsiveness
            const width = window.innerWidth;
            if (width >= 1200) {
                addResult('Screen Size', 'pass', `Desktop view (${width}px) - all features available`);
            } else if (width >= 768) {
                addResult('Screen Size', 'pass', `Tablet view (${width}px) - responsive layout active`);
            } else {
                addResult('Screen Size', 'pass', `Mobile view (${width}px) - mobile layout active`);
            }
            
            // Test 6: Check JavaScript functionality
            try {
                const testArray = [1, 2, 3];
                const shuffled = testArray.sort(() => Math.random() - 0.5);
                addResult('JavaScript', 'pass', 'JavaScript working correctly');
            } catch (error) {
                addResult('JavaScript', 'fail', 'JavaScript error: ' + error.message);
            }
            
            // Test 7: Check CSS support
            const testElement = document.createElement('div');
            testElement.style.display = 'flex';
            if (testElement.style.display === 'flex') {
                addResult('CSS Support', 'pass', 'Modern CSS features supported');
            } else {
                addResult('CSS Support', 'fail', 'Limited CSS support - may affect layout');
            }
            
            addResult('Test Suite', 'pass', 'All tests completed! Check individual results above.');
        }
        
        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
    
    <h2>📋 Manual Testing Checklist</h2>
    <p>After running the automated tests above, manually test these features on your main website:</p>
    
    <h3>✅ Core Features to Test:</h3>
    <ol>
        <li><strong>Learn Section:</strong> Check/uncheck modules, verify progress bar updates</li>
        <li><strong>Timeline:</strong> Switch between Explore and Sort tabs, test drag-and-drop</li>
        <li><strong>Quizzes:</strong> Complete modules to unlock quizzes, test different question types</li>
        <li><strong>Practice:</strong> Type answers, show/hide model answers</li>
        <li><strong>Navigation:</strong> Click all menu items, test mobile menu</li>
        <li><strong>Offline:</strong> Disconnect internet, refresh page, verify it still works</li>
    </ol>
    
    <h3>🎯 Success Indicators:</h3>
    <ul>
        <li>✅ No JavaScript errors in browser console</li>
        <li>✅ Progress tracking works and persists</li>
        <li>✅ Quizzes unlock after completing modules</li>
        <li>✅ Timeline sorting provides feedback</li>
        <li>✅ Website works offline</li>
        <li>✅ Mobile layout is usable</li>
    </ul>
    
    <h3>🚨 Common Issues:</h3>
    <ul>
        <li><strong>Service Worker not working:</strong> Use localhost instead of file:// protocol</li>
        <li><strong>Progress not saving:</strong> Check if localStorage is enabled</li>
        <li><strong>Drag-and-drop not working:</strong> Try on desktop browser first</li>
        <li><strong>Quizzes not unlocking:</strong> Verify all required modules are checked</li>
    </ul>
    
    <p><strong>Next Step:</strong> Open your main website (<code>index.html</code>) and go through the manual checklist above!</p>
</body>
</html>
