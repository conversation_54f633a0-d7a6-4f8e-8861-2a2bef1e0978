# 🧪 GCSE History Website Testing Checklist

Use this checklist to systematically test all features of your revision website.

## ✅ Basic Functionality Tests

### Navigation
- [ ] Click each navigation link (Home, Learn, Timeline, Practice, Quizzes, Exam Tips)
- [ ] Test mobile menu (resize browser window or use mobile device)
- [ ] Verify smooth scrolling to sections
- [ ] Check that all links work correctly

### Home Section
- [ ] Verify welcome message displays correctly
- [ ] Check that feature overview is readable
- [ ] Test "Start Learning" button links to Learn section
- [ ] Confirm responsive design on different screen sizes

## 📚 Learn Section Tests

### Progress Tracking
- [ ] Check that progress bar starts at 0/9 modules
- [ ] Verify progress text shows "0/9 modules" initially
- [ ] Test that progress updates when modules are completed

### Module Completion
- [ ] Click accordion buttons to expand/collapse topics
- [ ] Test module checkboxes:
  - [ ] Check Module 1.1 - verify it turns green (module-complete class)
  - [ ] Check Module 1.2 - verify progress bar updates
  - [ ] Check Module 1.3 - verify progress shows 3/9
  - [ ] Uncheck a module - verify progress decreases
- [ ] Refresh page - verify completed modules stay checked (localStorage)

### Content Display
- [ ] Verify all 9 modules display correctly:
  - [ ] Topic 1: Modules 1.1, 1.2, 1.3
  - [ ] Topic 2: Modules 2.1, 2.2, 2.3  
  - [ ] Topic 3: Modules 3.1, 3.2, 3.3
- [ ] Check that emojis display correctly
- [ ] Verify bullet points and bold text formatting
- [ ] Test "Download Complete Summary" button

## ⏰ Timeline Section Tests

### Tab Switching
- [ ] Click "Explore Events" tab - verify content shows
- [ ] Click "Sort Challenge" tab - verify content switches
- [ ] Check tab styling updates correctly (blue for active)

### Explore Events Mode
- [ ] Test filter buttons:
  - [ ] "All Events" - should show all 26 events
  - [ ] "1941-1958" - should filter to Origins period
  - [ ] "1958-1970" - should filter to Crises period
  - [ ] "1970-1991" - should filter to End period
- [ ] Click event cards - verify details appear (tooltip/modal)
- [ ] Check hover effects on timeline cards

### Sort Challenge Mode
- [ ] Verify 8 events appear in random order
- [ ] Test drag and drop functionality:
  - [ ] Drag an event - verify it moves
  - [ ] Drop in different position - verify it stays
  - [ ] Check visual feedback during dragging
- [ ] Click "Check Order" button:
  - [ ] If correct order - verify success message
  - [ ] If incorrect - verify feedback with explanations
- [ ] Click "Reset" button - verify events randomize again
- [ ] Test multiple attempts to verify randomization

## 🎯 Quizzes Section Tests

### Prerequisites System
- [ ] Initially, all quizzes should be locked (gray dots, disabled buttons)
- [ ] Complete modules 1.1, 1.2, 1.3:
  - [ ] Origins quiz should unlock (green dot, enabled button)
  - [ ] Other quizzes should remain locked
- [ ] Complete modules 2.1, 2.2, 2.3:
  - [ ] Crises quiz should unlock
- [ ] Complete modules 3.1, 3.2, 3.3:
  - [ ] End quiz should unlock

### Quiz Functionality
- [ ] Click "Start Quiz" on unlocked quiz
- [ ] Verify quiz interface appears with:
  - [ ] Question counter (1/10)
  - [ ] Score display (0/0)
  - [ ] Progress bar
  - [ ] Question content
- [ ] Test different question types:
  - [ ] Multiple choice - click options, verify feedback
  - [ ] Fill-in-gaps - type answers, verify validation
  - [ ] Matching - drag/click pairs, verify matching
- [ ] Complete full quiz:
  - [ ] Verify final score display
  - [ ] Check that best score is saved
  - [ ] Test "Try Again" functionality

## ❓ Practice Section Tests

### Question Display
- [ ] Verify source questions display with mock sources
- [ ] Check 12-mark and 16-mark questions appear
- [ ] Test key dates reference section

### Answer Functionality
- [ ] Type in answer textareas
- [ ] Refresh page - verify answers are saved (localStorage)
- [ ] Click "Show Model Answer" buttons:
  - [ ] Verify model answers appear
  - [ ] Click again - verify they hide
  - [ ] Check button text changes appropriately

## 💡 Exam Tips Tests

- [ ] Verify all tip sections display correctly
- [ ] Check time management breakdown
- [ ] Verify essential dates and facts are readable
- [ ] Test responsive layout on mobile

## 💾 Offline Functionality Tests

### Service Worker
- [ ] Open browser developer tools (F12)
- [ ] Go to Application/Storage tab
- [ ] Check that Service Worker is registered
- [ ] Verify cache contains website files

### Offline Testing
- [ ] With website loaded, disconnect internet
- [ ] Refresh page - should still work
- [ ] Test all features offline:
  - [ ] Module completion
  - [ ] Timeline sorting
  - [ ] Quiz functionality
  - [ ] Practice questions
- [ ] Reconnect internet - verify everything still works

## 📱 Responsive Design Tests

### Desktop (1200px+)
- [ ] Verify grid layouts display correctly
- [ ] Check navigation is horizontal
- [ ] Test all features at full width

### Tablet (768px-1199px)
- [ ] Verify responsive grid adjustments
- [ ] Check navigation remains accessible
- [ ] Test touch interactions

### Mobile (320px-767px)
- [ ] Verify mobile menu appears
- [ ] Check content stacks vertically
- [ ] Test touch interactions for sorting
- [ ] Verify text remains readable

## 🔧 Browser Compatibility Tests

Test in multiple browsers:
- [ ] Chrome
- [ ] Firefox
- [ ] Edge
- [ ] Safari (if available)

## 🐛 Error Testing

### Edge Cases
- [ ] Try to access quiz without completing modules
- [ ] Test with JavaScript disabled (should show basic content)
- [ ] Clear localStorage and verify reset functionality
- [ ] Test with very long answers in practice questions

### Performance
- [ ] Check page load speed
- [ ] Verify smooth animations
- [ ] Test with slow internet connection

## ✅ Final Verification

- [ ] All 9 modules can be completed
- [ ] All 3 quizzes can be unlocked and completed
- [ ] Timeline sorting works correctly
- [ ] Practice questions save answers
- [ ] Offline functionality works
- [ ] Mobile experience is smooth
- [ ] Progress persists across browser sessions

## 🎯 Success Criteria

Your website passes testing if:
- ✅ All features work without errors
- ✅ Progress tracking functions correctly
- ✅ Teaching-before-testing is enforced
- ✅ Offline functionality works
- ✅ Mobile experience is smooth
- ✅ Content is accurate and helpful

## 🚨 Common Issues to Check

1. **Modules not unlocking quizzes**: Check module IDs match quiz requirements
2. **Progress not saving**: Verify localStorage is enabled
3. **Timeline sorting not working**: Check drag and drop event handlers
4. **Offline not working**: Verify Service Worker registration
5. **Mobile menu not appearing**: Check responsive breakpoints

---

**Testing Time Estimate**: 30-45 minutes for complete testing
**Recommended**: Test on both desktop and mobile devices
